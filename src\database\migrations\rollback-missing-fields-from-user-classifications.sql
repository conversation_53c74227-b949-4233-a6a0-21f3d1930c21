-- Rollback Migration: Remove added fields from user_classifications table
-- Date: 2024-12-19
-- Description: <PERSON><PERSON><PERSON> các trường đã thêm khỏi bảng user_classifications

-- Kiểm tra và xóa các cột đã thêm từ bảng user_classifications
DO $$
BEGIN
    -- Xóa cột duration
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'duration'
    ) THEN
        ALTER TABLE user_classifications DROP COLUMN duration;
        RAISE NOTICE 'Đã xóa cột duration khỏi bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột duration không tồn tại trong bảng user_classifications';
    END IF;

    -- Xóa cột start_time
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'start_time'
    ) THEN
        ALTER TABLE user_classifications DROP COLUMN start_time;
        RAISE NOTICE 'Đã xóa cột start_time khỏi bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột start_time không tồn tại trong bảng user_classifications';
    END IF;

    -- Xóa cột end_time
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'end_time'
    ) THEN
        ALTER TABLE user_classifications DROP COLUMN end_time;
        RAISE NOTICE 'Đã xóa cột end_time khỏi bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột end_time không tồn tại trong bảng user_classifications';
    END IF;

    -- Xóa cột timezone
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'timezone'
    ) THEN
        ALTER TABLE user_classifications DROP COLUMN timezone;
        RAISE NOTICE 'Đã xóa cột timezone khỏi bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột timezone không tồn tại trong bảng user_classifications';
    END IF;

    -- Xóa cột status
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'status'
    ) THEN
        ALTER TABLE user_classifications DROP COLUMN status;
        RAISE NOTICE 'Đã xóa cột status khỏi bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột status không tồn tại trong bảng user_classifications';
    END IF;

    -- Xóa cột quantity
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'quantity'
    ) THEN
        ALTER TABLE user_classifications DROP COLUMN quantity;
        RAISE NOTICE 'Đã xóa cột quantity khỏi bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột quantity không tồn tại trong bảng user_classifications';
    END IF;

END $$;

-- Kiểm tra kết quả rollback
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'user_classifications' 
ORDER BY ordinal_position;

-- Hiển thị cấu trúc bảng sau khi rollback
\d user_classifications;
