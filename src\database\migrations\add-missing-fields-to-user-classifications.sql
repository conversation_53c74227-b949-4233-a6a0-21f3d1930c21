-- Migration: Add missing fields to user_classifications table
-- Date: 2024-12-19
-- Description: Thê<PERSON> các trường còn thiếu vào bảng user_classifications để khớp với UserClassification entity

-- <PERSON><PERSON><PERSON> tra và thêm các cột còn thiếu vào bảng user_classifications
DO $$
BEGIN
    -- Thêm cột duration (thời lượng dịch vụ - phút)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'duration'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN duration INTEGER NULL;
        
        COMMENT ON COLUMN user_classifications.duration 
        IS 'Thời lượng dịch vụ (phút) - cho service packages';
        
        RAISE NOTICE 'Đã thêm cột duration vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột duration đã tồn tại trong bảng user_classifications';
    END IF;

    -- Thêm cột start_time (thời gian bắt đầu - timestamp)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'start_time'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN start_time BIGINT NULL;
        
        COMMENT ON COLUMN user_classifications.start_time 
        IS 'Thời gian bắt đầu (timestamp) - cho service packages';
        
        RAISE NOTICE 'Đã thêm cột start_time vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột start_time đã tồn tại trong bảng user_classifications';
    END IF;

    -- Thêm cột end_time (thời gian kết thúc - timestamp)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'end_time'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN end_time BIGINT NULL;
        
        COMMENT ON COLUMN user_classifications.end_time 
        IS 'Thời gian kết thúc (timestamp) - cho service packages';
        
        RAISE NOTICE 'Đã thêm cột end_time vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột end_time đã tồn tại trong bảng user_classifications';
    END IF;

    -- Thêm cột timezone (múi giờ)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'timezone'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN timezone VARCHAR(100) NULL;
        
        COMMENT ON COLUMN user_classifications.timezone 
        IS 'Múi giờ - cho service packages';
        
        RAISE NOTICE 'Đã thêm cột timezone vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột timezone đã tồn tại trong bảng user_classifications';
    END IF;

    -- Thêm cột status (trạng thái)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'status'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN status VARCHAR(50) NULL;
        
        COMMENT ON COLUMN user_classifications.status 
        IS 'Trạng thái - cho service packages';
        
        RAISE NOTICE 'Đã thêm cột status vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột status đã tồn tại trong bảng user_classifications';
    END IF;

    -- Thêm cột quantity (số lượng có sẵn)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'quantity'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN quantity INTEGER NULL;
        
        COMMENT ON COLUMN user_classifications.quantity 
        IS 'Số lượng có sẵn - cho service packages';
        
        RAISE NOTICE 'Đã thêm cột quantity vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột quantity đã tồn tại trong bảng user_classifications';
    END IF;

    -- Kiểm tra và thêm cột custom_fields nếu chưa có
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'custom_fields'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN custom_fields JSONB NULL;
        
        COMMENT ON COLUMN user_classifications.custom_fields 
        IS 'Trường tùy chỉnh (jsonb)';
        
        RAISE NOTICE 'Đã thêm cột custom_fields vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột custom_fields đã tồn tại trong bảng user_classifications';
    END IF;

    -- Kiểm tra và thêm cột images_media nếu chưa có
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_classifications' 
        AND column_name = 'images_media'
    ) THEN
        ALTER TABLE user_classifications 
        ADD COLUMN images_media JSONB NULL;
        
        COMMENT ON COLUMN user_classifications.images_media 
        IS 'Thông tin media hình ảnh (jsonb)';
        
        RAISE NOTICE 'Đã thêm cột images_media vào bảng user_classifications';
    ELSE
        RAISE NOTICE 'Cột images_media đã tồn tại trong bảng user_classifications';
    END IF;

END $$;

-- Kiểm tra kết quả migration
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'user_classifications' 
ORDER BY ordinal_position;

-- Hiển thị cấu trúc bảng sau khi migration
\d user_classifications;
