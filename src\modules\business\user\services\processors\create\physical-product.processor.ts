import { Injectable, Logger } from '@nestjs/common';
import {
  InventoryRepository,
  PhysicalWarehouseRepository,
} from '@modules/business/repositories';
import { Inventory } from '@modules/business/entities';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PriceTypeEnum } from '@modules/business/enums';
import { InventoryInfo, InventoryInfoUtils } from '@modules/business/types';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { ClassificationService } from '../../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { PhysicalProductCreateDto } from '../../../dto/request/create/physical-product.dto';
import { ProductInventoryDto } from '../../../dto/product-inventory.dto';
import { CreateClassificationDto, ClassificationResponseDto } from '../../../dto/classification.dto';

/**
 * Processor chuyên xử lý logic tạo sản phẩm vật lý
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class PhysicalProductProcessor {
  private readonly logger = new Logger(PhysicalProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly classificationService: ClassificationService,
  ) {}

  /**
   * Tạo sản phẩm vật lý hoàn chỉnh
   */
  async createPhysicalProduct(
    dto: PhysicalProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating PHYSICAL product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm vật lý
    await this.validatePhysicalProductData(dto);

    // BƯỚC 2: Xử lý custom fields và tạo metadata
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto);

    // BƯỚC 3: Tạo entity sản phẩm cơ bản
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);

    // BƯỚC 4: Lưu sản phẩm vào database để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Xử lý inventory (kho hàng) - chỉ xử lý nếu có inventory
    const inventories = await this.processPhysicalInventory(savedProduct.id, dto.inventory || [], userId);

    // BƯỚC 8: Xử lý classifications (phân loại sản phẩm)
    const { classifications, classificationUploadUrls } = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 9: Lấy sản phẩm cuối cùng sau khi đã xử lý xong
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 10: Tạo object chứa upload URLs cho frontend
    const allUploadUrls = [...imagesUploadUrls, ...classificationUploadUrls];
    const uploadUrls = allUploadUrls.length > 0 ? {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls,
      classificationUploadUrls: classificationUploadUrls
    } : null;

    // Trả về kết quả cuối cùng
    return {
      product: finalProduct,
      uploadUrls,
      additionalInfo: {
        inventory: inventories, // Đổi tên để tương thích với interface
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm vật lý
   */
  private async validatePhysicalProductData(dto: PhysicalProductCreateDto): Promise<void> {
    // Kiểm tra inventory nếu có được truyền vào
    if (dto.inventory && Array.isArray(dto.inventory) && dto.inventory.length > 0) {
      // Kiểm tra từng inventory trong mảng
      for (const inventory of dto.inventory) {
        if (!inventory.warehouseId && !inventory.inventoryId) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
            'Mỗi inventory phải có warehouseId hoặc inventoryId',
          );
        }
      }
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm vật lý)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Giá sản phẩm là bắt buộc đối với sản phẩm vật lý',
      );
    }

    // Validate giá sản phẩm theo business rules
    // Note: PhysicalProductCreateDto không có typePrice field, giả định HAS_PRICE cho sản phẩm vật lý
    this.validationHelper.validateProductPrice(dto.price, PriceTypeEnum.HAS_PRICE, dto.productType);

    this.logger.log(`Validated physical product data for: ${dto.name}`);
  }

  /**
   * Xử lý inventory cho sản phẩm vật lý (hỗ trợ nhiều kho)
   * Nếu không có inventory, sản phẩm sẽ được tạo mà không quản lý tồn kho
   */
  private async processPhysicalInventory(
    productId: number,
    inventoryDtos: any[],
    userId: number,
  ): Promise<any[]> {
    if (!inventoryDtos || inventoryDtos.length === 0) {
      this.logger.log(`No inventory provided for physical product: ${productId} - Product will be created without inventory management`);
      return [];
    }

    this.logger.log(`Processing ${inventoryDtos.length} inventories for physical product: ${productId}`);

    // Tạo các bản ghi inventory cho sản phẩm vật lý ở nhiều kho
    const inventories = await Promise.all(
      inventoryDtos.map(inventoryDto =>
        this.createInventoryForPhysicalProduct(productId, inventoryDto, userId)
      )
    );

    return inventories;
  }

  /**
   * Tạo bản ghi inventory cho sản phẩm vật lý
   */
  private async createInventoryForPhysicalProduct(
    productId: number,
    inventoryDto: ProductInventoryDto,
    userId: number,
  ): Promise<Inventory> {
    this.logger.log(`Creating inventory for product ${productId} at warehouse ${inventoryDto.warehouseId}`);

    try {
      // Tạo inventory mới
      const newInventory = new Inventory();
      newInventory.productId = productId;
      newInventory.warehouseId = inventoryDto.warehouseId || null;
      newInventory.sku = inventoryDto.sku || null;
      newInventory.barcode = inventoryDto.barcode || null;

      // Thiết lập số lượng
      const availableQuantity = inventoryDto.availableQuantity || 0;
      newInventory.availableQuantity = availableQuantity;
      newInventory.currentQuantity = availableQuantity;
      newInventory.totalQuantity = availableQuantity;
      newInventory.reservedQuantity = 0;
      newInventory.defectiveQuantity = 0;
      newInventory.lastUpdated = Date.now();

      // Tạo thông tin chi tiết trong field info
      const inventoryInfo: InventoryInfo = InventoryInfoUtils.createInitialInfo({
        initialBatch: {
          sku: inventoryDto.sku || null,
          barcode: inventoryDto.barcode || null,
          currentQuantity: availableQuantity,
          totalQuantity: availableQuantity,
          availableQuantity: availableQuantity,
          reservedQuantity: 0,
          defectiveQuantity: 0,
          status: 'ACTIVE',
          notes: `Initial inventory for product ${productId}`,
        },
        metadata: {
          notes: `Created for product ${productId} at warehouse ${inventoryDto.warehouseId}`,
          status: 'ACTIVE',
        },
      });

      newInventory.info = inventoryInfo;

      // Lưu vào database
      const savedInventory = await this.inventoryRepository.save(newInventory);

      this.logger.log(`Successfully created inventory ${savedInventory.id} for product ${productId}`);
      return savedInventory;

    } catch (error) {
      this.logger.error(`Error creating inventory for product ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Không thể tạo inventory cho sản phẩm ${productId}: ${error.message}`,
      );
    }
  }


  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: any[],
    userId: number,
  ): Promise<{
    classifications: ClassificationResponseDto[];
    classificationUploadUrls: any[];
  }> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return {
        classifications: [],
        classificationUploadUrls: []
      };
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for product: ${productId}`);

    const createdClassifications: ClassificationResponseDto[] = [];
    const classificationUploadUrls: any[] = [];

    // Tạo từng classification
    for (let index = 0; index < classificationsDto.length; index++) {
      const classificationDto = classificationsDto[index];

      try {
        // Mapping từ request DTO sang CreateClassificationDto
        const createClassificationDto = this.mapToCreateClassificationDto(classificationDto);

        // Tạo classification thông qua ClassificationService
        const createdClassification = await this.classificationService.create(
          productId,
          createClassificationDto,
          userId,
        );

        createdClassifications.push(createdClassification);

        // Tạo presigned URLs cho ảnh classification nếu có
        if (classificationDto.imagesMediaTypes && classificationDto.imagesMediaTypes.length > 0) {
          const uploadUrls = await this.createClassificationImageUploadUrls(
            classificationDto,
            createdClassification.id,
            index
          );
          classificationUploadUrls.push(...uploadUrls);
        }

        this.logger.log(`Created classification: ${createdClassification.id} for product: ${productId}`);
      } catch (error) {
        this.logger.error(`Failed to create classification for product ${productId}: ${error.message}`, error.stack);
        throw error;
      }
    }

    this.logger.log(`Successfully created ${createdClassifications.length} classifications for physical product: ${productId}`);

    return {
      classifications: createdClassifications,
      classificationUploadUrls
    };
  }

  /**
   * Mapping từ request DTO sang CreateClassificationDto
   */
  private mapToCreateClassificationDto(classificationDto: any): CreateClassificationDto {
    return {
      type: classificationDto.type,
      description: classificationDto.description || null,
      price: classificationDto.price,
      customFields: classificationDto.customFields || [],
      imagesMediaTypes: classificationDto.imagesMediaTypes || [],
      sku: classificationDto.sku || null,
      availableQuantity: classificationDto.availableQuantity,
      minQuantityPerPurchase: classificationDto.minQuantityPerPurchase,
      maxQuantityPerPurchase: classificationDto.maxQuantityPerPurchase,
    };
  }

  /**
   * Tạo presigned URLs cho ảnh classification
   */
  private async createClassificationImageUploadUrls(
    classificationDto: any,
    classificationId: number,
    classificationIndex: number
  ): Promise<any[]> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    if (classificationDto.imagesMediaTypes && classificationDto.imagesMediaTypes.length > 0) {
      for (let i = 0; i < classificationDto.imagesMediaTypes.length; i++) {
        const mediaType = classificationDto.imagesMediaTypes[i];
        const fileName = `physical-classification-${classificationId}-image-${i}-${timestamp}`;

        // TODO: Implement actual S3 presigned URL generation
        // Hiện tại tạo placeholder URL, cần implement thực tế với S3Service
        uploadUrls.push({
          url: `https://presigned-url-example.com/${fileName}`,
          key: fileName,
          index: i,
          classificationId: classificationId,
          classificationIndex: classificationIndex,
          classificationType: classificationDto.type
        });
      }
    }

    this.logger.log(`Created ${uploadUrls.length} upload URLs for classification ${classificationId} images`);
    return uploadUrls;
  }
}
